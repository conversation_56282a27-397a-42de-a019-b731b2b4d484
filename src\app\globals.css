@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Bridge image - Remove white background and make it a silhouette */
.bridge-image {
  background: transparent !important;
  /* Make it a black silhouette that will show against aurora */
  filter: brightness(0) contrast(200%) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  /* Alternative: if you want to remove white but keep some bridge color */
  /* mix-blend-mode: multiply; */
  /* filter: contrast(200%) saturate(0) brightness(0.3); */
}

/* If the above makes it too dark, try this alternative */
.bridge-image-alt {
  background: transparent !important;
  filter: 
    contrast(300%) 
    brightness(0.1) 
    saturate(0) 
    drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
  mix-blend-mode: multiply;
}