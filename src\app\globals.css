@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Bridge image - Remove white checkboard background */
.bridge-image {
  background: transparent !important;
  background-color: transparent !important;
  /* Remove white pixels and create clean silhouette */
  filter: contrast(200%) brightness(0) drop-shadow(0 0 0 black);
  /* Alternative: use multiply blend mode for better integration */
  mix-blend-mode: multiply;
}

.bridge-image img {
  background: transparent !important;
  background-color: transparent !important;
  /* Ensure the actual image element also has no background */
  filter: contrast(200%) brightness(0);
}
