@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Bridge image - Perfect size and visibility */
.bridge-image {
  background: transparent !important;
  /* Enhanced filters for perfect bridge appearance */
  filter:
    contrast(180%)
    saturate(140%)
    brightness(1.3)
    drop-shadow(0 8px 16px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 20px rgba(123, 58, 237, 0.3));
  /* Smooth scaling for better quality */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* If the above makes it too dark, try this alternative */
.bridge-image-alt {
  background: transparent !important;
  filter: 
    contrast(300%) 
    brightness(0.1) 
    saturate(0) 
    drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
  mix-blend-mode: multiply;
}