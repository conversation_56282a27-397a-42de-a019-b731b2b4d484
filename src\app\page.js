"use client";

import Image from "next/image";
import Aurora from "./components/Aurora";

export default function Home() {
  return (
     <div className="relative min-h-screen bg-black overflow-hidden">
      {/* Black Background */}
      <div className="fixed inset-0 z-0 bg-black"></div>

      {/* Logo - Left Top Corner */}
      <div className="fixed top-4 left-4 z-30 w-24 h-24 md:w-32 md:h-32 lg:w-40 lg:h-40">
        <Image
          src="/LOGO__1_-removebg-preview.png"
          alt="Logo"
          fill
          className="object-contain"
          priority
        />
      </div>
      
      {/* Aurora Effect - Behind the bridge */}
      <div className="fixed inset-0 z-10 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Bridge with Transparent Background - Extreme Right Bottom Position - Larger Size */}
      <div className="fixed bottom-0 right-0 z-20 w-[600px] h-96 md:w-[700px] md:h-[450px] lg:w-[800px] lg:h-[500px] xl:w-[900px] xl:h-[550px]">
        <Image
          src="/bridge-transparent-background-with-blue-line_545677-12672-removebg-preview.png"
          alt="Bridge with Blue Line - Transparent Background"
          fill
          className="object-contain object-right-bottom bridge-image"
          priority
        />
      </div>

      {/* Aurora Effect */}
      <div className="fixed inset-0 z-20 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Your content goes here */}
      <div className="relative z-30 p-8 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>
            Welcome to<br />Cloud Native Durgapur
          </h1>
        </div>
      </div>
    </div>
  );
}
