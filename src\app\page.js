"use client";

import Image from "next/image";
import Aurora from "./components/Aurora";

export default function Home() {
  return (
    <div className="relative min-h-screen bg-black">
      {/* Black Background */}
      <div className="fixed inset-0 z-0 bg-black"></div>

      {/* Bridge Silhouette - Full image visible, smaller size */}
      <div className="fixed inset-0 z-10 flex items-center justify-center">
        <div className="relative w-96 h-64 md:w-[600px] md:h-96">
          <Image
            src="/images.jpeg"
            alt="Bridge Silhouette"
            fill
            className="object-contain"
            style={{
              mixBlendMode: 'screen',
              filter: 'contrast(200%) brightness(0.3) invert(1)',
              opacity: 0.8
            }}
            priority
          />
        </div>
      </div>

      {/* Aurora Effect */}
      <div className="fixed inset-0 z-20 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Your content goes here */}
      <div className="relative z-30 p-8">
        <h1 className="text-4xl font-bold text-white text-center drop-shadow-lg">
          Welcome to Aurora Background
        </h1>
        <p className="text-white text-center mt-4 drop-shadow-md">
          Bridge silhouette with beautiful aurora effects
        </p>
      </div>
    </div>
  );
}
