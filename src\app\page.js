"use client";

import Image from "next/image";
import Link from "next/link";
import Aurora from "./components/Aurora";

export default function Home() {
  return (
    <div className="relative bg-black">
      {/* Black Background */}
      <div className="fixed inset-0 z-0 bg-black"></div>

      {/* Aurora Effect - Behind everything */}
      <div className="fixed inset-0 z-10 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Logo - Left Top Corner */}
      <div className="fixed top-4 left-4 z-30 w-24 h-24 md:w-32 md:h-32 lg:w-40 lg:h-40">
        <Link href="/">
          <Image
            src="/LOGO__1_-removebg-preview.png"
            alt="Logo"
            fill
            className="object-contain cursor-pointer"
            priority
          />
        </Link>
      </div>

      {/* Navigation Bar - Centered */}
      <nav className="fixed top-4 left-1/2 transform -translate-x-1/2 z-30">
        <ul className="flex flex-wrap gap-4 md:gap-6 lg:gap-8 text-white justify-center" style={{ fontFamily: 'Michroma, sans-serif' }}>
          <li><a href="#home" className="text-sm md:text-base hover:text-purple-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">Home</a></li>
          <li><a href="#about" className="text-sm md:text-base hover:text-green-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">About</a></li>
          <li><a href="#venue" className="text-sm md:text-base hover:text-blue-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">Venue</a></li>
          <li><a href="#sponsors" className="text-sm md:text-base hover:text-purple-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">Sponsors</a></li>
          <li><a href="#speakers" className="text-sm md:text-base hover:text-green-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">Speakers</a></li>
          <li><a href="#testimonials" className="text-sm md:text-base hover:text-purple-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">Testimonials</a></li>
          <li><a href="#faq" className="text-sm md:text-base hover:text-green-300 transition-colors duration-300 drop-shadow-lg cursor-pointer">FAQ</a></li>
        </ul>
      </nav>
      
      {/* Aurora Effect - Behind the bridge */}
      <div className="fixed inset-0 z-10 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Bridge with Transparent Background - Right Bottom Position */}
      <div className="fixed right-0 bottom-12 z-20 flex items-end justify-end w-[600px] h-96 md:w-[700px] md:h-[450px] lg:w-[800px] lg:h-[500px] xl:w-[900px] xl:h-[550px]">
        <Image
          src="/bridge-transparent-background-with-blue-line_545677-12672-removebg-preview.png"
          alt="Bridge with Blue Line - Transparent Background"
          fill
          className="object-contain object-right-bottom bridge-image"
          priority
        />
      </div>

      {/* Marquee Section - Right after bridge image, no spacing */}
      <div className="fixed bottom-0 left-0 right-0 z-25 bg-black bg-opacity-95 overflow-hidden">
        <marquee className="text-xl md:text-2xl lg:text-3xl font-bold text-white drop-shadow-lg py-3" style={{ fontFamily: 'Michroma, sans-serif' }} direction="right" scrollamount="5">
          see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!! • see you soon!!
        </marquee>
      </div>
      <div className="fixed inset-0 z-20 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Home Section */}
      <section id="home" className="relative z-30 flex flex-col items-center justify-center p-8 min-h-screen" style={{ marginTop: '-80px' }}>
        <span className="block text-lg md:text-xl lg:text-2xl text-white mb-2 font-bold" style={{ fontFamily: 'Michroma, sans-serif' }}>
          Welcome to
        </span>
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>
          Cloud Native Durgapur
        </h1>
      </section>



      {/* About Section */}
      <section id="about" className="relative z-30 min-h-screen p-8 flex items-center justify-center bg-black bg-opacity-70">
        <div className="max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>About</h2>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md mb-6">
            Cloud Native Durgapur is a community-driven initiative focused on promoting cloud-native technologies,
            DevOps practices, and modern software development methodologies in the Durgapur region.
          </p>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md">
            We bring together developers, DevOps engineers, and technology enthusiasts to learn, share, and grow together
            in the rapidly evolving world of cloud-native computing.
          </p>
        </div>
      </section>

      {/* Venue Section */}
      <section id="venue" className="relative z-30 min-h-screen p-8 flex items-center justify-center bg-black bg-opacity-70">
        <div className="max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>Venue</h2>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md mb-6">
            Join us at our state-of-the-art venue in Durgapur for an immersive experience in cloud-native technologies.
          </p>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md">
            Our venue features modern facilities, high-speed internet, and comfortable seating arrangements
            perfect for workshops, presentations, and networking sessions.
          </p>
        </div>
      </section>

      {/* Sponsors Section */}
      <section id="sponsors" className="relative z-30 min-h-screen p-8 flex items-center justify-center bg-black bg-opacity-70">
        <div className="max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>Sponsors</h2>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md mb-6">
            We are grateful to our sponsors who make this community possible and help us organize world-class events.
          </p>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md">
            Interested in sponsoring? Contact us to learn about partnership opportunities and how you can support
            the growing cloud-native community in Durgapur.
          </p>
        </div>
      </section>

      {/* Speakers Section */}
      <section id="speakers" className="relative z-30 min-h-screen p-8 flex items-center justify-center bg-black bg-opacity-70">
        <div className="max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>Speakers</h2>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md mb-6">
            Meet our expert speakers who will share their knowledge on cloud-native technologies,
            DevOps best practices, and the future of software development.
          </p>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md">
            Our speakers include industry leaders, open-source contributors, and experienced practitioners
            who bring real-world insights and practical knowledge to our community.
          </p>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="relative z-30 min-h-screen p-8 flex items-center justify-center bg-black bg-opacity-70">
        <div className="max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>Testimonials</h2>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md mb-6">
            Hear from our community members about their experiences with Cloud Native Durgapur
            and how it has helped them grow in their cloud-native journey.
          </p>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md">
            Our community has helped hundreds of professionals advance their careers, learn new technologies,
            and build meaningful connections in the tech industry.
          </p>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="relative z-30 min-h-screen p-8 flex items-center justify-center bg-black bg-opacity-70">
        <div className="max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 drop-shadow-lg" style={{ fontFamily: 'Michroma, sans-serif' }}>FAQ</h2>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md mb-6">
            Find answers to frequently asked questions about our events, membership,
            and how to get involved with the Cloud Native Durgapur community.
          </p>
          <p className="text-lg md:text-xl text-white leading-relaxed drop-shadow-md">
            Have a question that's not answered here? Feel free to reach out to us through our
            social media channels or contact form.
          </p>
        </div>
      </section>
    </div>
  );
}
