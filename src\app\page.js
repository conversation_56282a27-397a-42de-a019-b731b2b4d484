"use client";

import Image from "next/image";
import Aurora from "./components/Aurora";

export default function Home() {
  return (
    <div className="relative min-h-screen">
      {/* Background Image */}
      <div className="fixed inset-0 z-0">
        <Image
          src="/images.jpeg"
          alt="Background"
          fill
          className="object-cover"
          priority
        />
      </div>

      {/* Aurora Overlay */}
      <div className="fixed inset-0 z-10 opacity-70">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Your content goes here */}
      <div className="relative z-20 p-8">
        <h1 className="text-4xl font-bold text-white text-center drop-shadow-lg">
          Welcome to Aurora Background
        </h1>
        <p className="text-white text-center mt-4 drop-shadow-md">
          Your content appears over the beautiful aurora effect and background image
        </p>
      </div>
    </div>
  );
}
