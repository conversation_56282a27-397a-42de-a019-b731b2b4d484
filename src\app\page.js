"use client";

import Image from "next/image";
import Aurora from "./components/Aurora";

export default function Home() {
  return (
    <div className="relative min-h-screen bg-black">
      {/* Black Background */}
      <div className="fixed inset-0 z-0 bg-black"></div>

      {/* Bridge with Transparent Background */}
      <div className="fixed inset-0 z-10 flex items-center justify-center">
        <div className="relative w-96 h-64 md:w-[600px] md:h-96 bg-transparent">
          <Image
            src="/bridge-transparent-background-with-blue-line_545677-12672.avif"
            alt="Bridge with Transparent Background"
            fill
            className="object-contain bridge-image"
            style={{
              opacity: 0.9,
              background: 'transparent',
              backgroundColor: 'transparent'
            }}
            priority
          />
        </div>
      </div>

      {/* Aurora Effect */}
      <div className="fixed inset-0 z-20 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Your content goes here */}
      <div className="relative z-30 p-8">
        <h1 className="text-4xl font-bold text-white text-center drop-shadow-lg">
          Welcome to Aurora Background
        </h1>
        <p className="text-white text-center mt-4 drop-shadow-md">
          Bridge silhouette with beautiful aurora effects
        </p>
      </div>
    </div>
  );
}
