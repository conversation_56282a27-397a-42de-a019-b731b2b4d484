"use client";

import Image from "next/image";
import Aurora from "./components/Aurora";

export default function Home() {
  return (
     <div className="relative min-h-screen bg-black overflow-hidden">
      {/* Black Background */}
      <div className="fixed inset-0 z-0 bg-black"></div>
      
      {/* Aurora Effect - Behind the bridge */}
      <div className="fixed inset-0 z-10 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Bridge with Transparent Background - Left Bottom Position */}
      <div className="fixed bottom-0 left-0 z-20 w-2/3 h-2/3 md:w-1/2 md:h-1/2 p-4">
        <div className="relative w-full h-full">
          <Image
            src="/bridge-transparent-background-with-blue-line_545677-12672-removebg-preview.png"
            alt="Bridge with Blue Line - Transparent Background"
            fill
            className="object-contain object-left-bottom bridge-image"
            priority
          />
        </div>
      </div>

      {/* Aurora Effect */}
      <div className="fixed inset-0 z-20 opacity-80">
        <Aurora
          colorStops={["#7C3AED", "#10B981", "#3B82F6"]}
          blend={0.8}
          amplitude={0.7}
          speed={0.8}
        />
      </div>

      {/* Your content goes here */}
      <div className="relative z-30 p-8">
        <h1 className="text-4xl font-bold text-white text-center drop-shadow-lg">
          Welcome to Aurora Background
        </h1>
        <p className="text-white text-center mt-4 drop-shadow-md">
          Bridge silhouette with beautiful aurora effects
        </p>
      </div>
    </div>
  );
}
